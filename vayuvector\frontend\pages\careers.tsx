import React from 'react';
import Link from 'next/link';
import Layout from '@/components/Layout';
import { 
  BriefcaseIcon, 
  UserGroupIcon, 
  GlobeAltIcon, 
  AcademicCapIcon,
  HeartIcon,
  TrophyIcon,
  ClockIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

const CareersPage: React.FC = () => {
  const benefits = [
    {
      title: "Competitive Compensation",
      description: "Attractive salary packages with performance bonuses",
      icon: CurrencyDollarIcon,
    },
    {
      title: "Global Opportunities",
      description: "Work with international teams and travel opportunities",
      icon: GlobeAltIcon,
    },
    {
      title: "Professional Development",
      description: "Continuous learning and career advancement programs",
      icon: AcademicCapIcon,
    },
    {
      title: "Work-Life Balance",
      description: "Flexible working hours and remote work options",
      icon: ClockIcon,
    },
    {
      title: "Health & Wellness",
      description: "Comprehensive health insurance and wellness programs",
      icon: HeartIcon,
    },
    {
      title: "Recognition Programs",
      description: "Employee recognition and achievement awards",
      icon: TrophyIcon,
    },
  ];

  const openPositions = [
    {
      title: "International Move Coordinator",
      department: "Operations",
      location: "Mumbai, India",
      type: "Full-time",
      description: "Coordinate international relocations and ensure smooth customer experience.",
      requirements: [
        "3+ years experience in logistics or customer service",
        "Excellent communication skills",
        "Knowledge of international shipping regulations",
        "Fluency in English and Hindi"
      ]
    },
    {
      title: "Customer Success Manager",
      department: "Customer Service",
      location: "Mumbai, India / Remote",
      type: "Full-time",
      description: "Build relationships with clients and ensure exceptional service delivery.",
      requirements: [
        "5+ years in customer success or account management",
        "Strong problem-solving abilities",
        "Experience with CRM systems",
        "Bachelor's degree preferred"
      ]
    },
    {
      title: "Business Development Executive",
      department: "Sales",
      location: "Mumbai, India",
      type: "Full-time",
      description: "Drive business growth through new client acquisition and partnerships.",
      requirements: [
        "3+ years in B2B sales",
        "Proven track record in lead generation",
        "Strong negotiation skills",
        "Experience in logistics industry preferred"
      ]
    },
    {
      title: "Operations Analyst",
      department: "Operations",
      location: "Mumbai, India",
      type: "Full-time",
      description: "Analyze operational data and optimize processes for efficiency.",
      requirements: [
        "2+ years in operations or data analysis",
        "Proficiency in Excel and data visualization tools",
        "Strong analytical and problem-solving skills",
        "Experience with process improvement"
      ]
    }
  ];

  const values = [
    {
      title: "Customer First",
      description: "We prioritize our customers' needs and exceed their expectations in every interaction."
    },
    {
      title: "Global Mindset",
      description: "We embrace diversity and think globally while acting locally in all our operations."
    },
    {
      title: "Innovation",
      description: "We continuously seek new ways to improve our services and customer experience."
    },
    {
      title: "Integrity",
      description: "We conduct business with honesty, transparency, and ethical practices."
    }
  ];

  return (
    <Layout
      title="Careers - VayuVector"
      description="Join VayuVector's global team and build a rewarding career in international relocation services. Explore our open positions and company culture."
    >
      {/* Hero Section */}
      <section className="pt-16 pb-16 bg-gradient-to-br from-gray-50 to-accent-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <BriefcaseIcon className="h-12 w-12 text-accent-600" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                Join Our Team
              </h1>
            </div>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              Build a rewarding career with VayuVector and help people move their lives 
              across the globe. We're looking for passionate individuals to join our 
              growing international team.
            </p>
            <Link 
              href="#positions" 
              className="btn-accent text-white font-semibold text-lg px-8 py-3"
            >
              View Open Positions
            </Link>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Values
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              At VayuVector, our values guide everything we do and shape our company culture.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="card p-6 text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h3>
                <p className="text-gray-600 text-sm">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Work With Us?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We offer competitive benefits and a supportive work environment that helps 
              you grow both professionally and personally.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon;
              return (
                <div key={index} className="card p-6">
                  <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mb-4">
                    <IconComponent className="h-6 w-6 text-accent-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section id="positions" className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Open Positions
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Explore our current job openings and find the perfect role to advance your career.
            </p>
          </div>

          <div className="space-y-8">
            {openPositions.map((position, index) => (
              <div key={index} className="card p-8">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{position.title}</h3>
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                      <span className="flex items-center space-x-1">
                        <UserGroupIcon className="h-4 w-4" />
                        <span>{position.department}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <GlobeAltIcon className="h-4 w-4" />
                        <span>{position.location}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <ClockIcon className="h-4 w-4" />
                        <span>{position.type}</span>
                      </span>
                    </div>
                  </div>
                  <div className="mt-4 lg:mt-0">
                    <a
                      href={`mailto:<EMAIL>?subject=Application for ${position.title}`}
                      className="btn-accent text-white font-semibold px-6 py-2"
                    >
                      Apply Now
                    </a>
                  </div>
                </div>

                <p className="text-gray-600 mb-6">{position.description}</p>

                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">Requirements:</h4>
                  <ul className="space-y-2">
                    {position.requirements.map((req, reqIndex) => (
                      <li key={reqIndex} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-accent-600 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-700">{req}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>

          {/* No suitable position */}
          <div className="card p-8 mt-12 bg-accent-50 border-accent-200">
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Don't see a suitable position?
              </h3>
              <p className="text-gray-600 mb-6">
                We're always looking for talented individuals to join our team. 
                Send us your resume and we'll keep you in mind for future opportunities.
              </p>
              <a
                href="mailto:<EMAIL>?subject=General Application"
                className="btn-accent text-white font-semibold px-6 py-3"
              >
                Send Your Resume
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="section bg-primary-900 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-lg mb-8 text-gray-300 max-w-2xl mx-auto">
            Join VayuVector and be part of a team that's making international relocation 
            easier for people around the world.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="btn bg-accent-600 text-white hover:bg-accent-700 text-lg px-8 py-3 font-semibold"
            >
              Contact HR Team
            </a>
            <Link
              href="/about"
              className="btn border-2 border-white text-white hover:bg-white hover:text-primary-900 text-lg px-8 py-3"
            >
              Learn About Us
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default CareersPage;
