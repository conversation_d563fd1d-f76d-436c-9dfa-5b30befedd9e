import React from 'react';
import Link from 'next/link';
import Layout from '@/components/Layout';
import QuoteCalculator from '@/components/QuoteCalculator';
import ServicesCarousel from '@/components/ServicesCarousel';
import Testimonials from '@/components/Testimonials';
import WorldMap from '@/components/WorldMap';

const HomePage: React.FC = () => {
  return (
    <Layout
      title="VayuVector - Your Global Relocation Partner"
      description="Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support for expats and professionals worldwide."
    >
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-hero-pattern"></div>
          {/* Additional visual elements */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="grid grid-cols-8 gap-4 w-full h-full p-8 opacity-10">
              {[...Array(64)].map((_, i) => (
                <div key={i} className="bg-gray-200 rounded-full opacity-30 animate-pulse" style={{ animationDelay: `${i * 0.1}s` }}></div>
              ))}
            </div>
          </div>
        </div>

        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gray-100/30 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gray-200/30 rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="relative z-10 container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Left Column - Text Content */}
            <div className="text-center lg:text-left">
              {/* Main Headline */}
              <h1 className="text-5xl md:text-7xl lg:text-8xl heading-main mb-8 animate-fade-in-up leading-tight">
                Your Global
                <span className="block text-navy-600">
                  Relocation Partner
                </span>
              </h1>

              {/* Tagline */}
              <p className="text-2xl md:text-3xl lg:text-4xl mb-10 heading-secondary animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
                Moving Lives, Not Just Belongings
              </p>

              {/* Description */}
              <p className="text-xl md:text-2xl mb-14 text-body animate-fade-in-up leading-relaxed" style={{ animationDelay: '0.4s' }}>
                Professional door-to-door relocation services for international professionals.
                From packing to destination setup, we handle every detail of your global move.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start items-center animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
                <Link href="/quote" className="bg-navy-700 text-white font-semibold text-xl px-10 py-5 rounded-lg hover:bg-navy-800 transition-colors duration-200 shadow-lg">
                  Get Free Quote
                </Link>
                <Link href="/how-it-works" className="border-2 border-navy-600 text-navy-600 font-semibold text-xl px-10 py-5 rounded-lg hover:bg-navy-600 hover:text-white transition-colors duration-200">
                  How It Works
                </Link>
              </div>
            </div>

            {/* Right Column - World Map */}
            <div className="relative animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
              <div className="relative w-full h-96 lg:h-[500px] bg-gradient-to-br from-navy-50 to-navy-100 rounded-2xl p-8 shadow-lg">
                {/* World Map SVG */}
                <svg viewBox="0 0 1000 500" className="w-full h-full">
                  {/* Simplified world map paths */}
                  <g fill="#e2e8f0" stroke="#cbd5e1" strokeWidth="1">
                    {/* North America */}
                    <path d="M150 150 L250 140 L280 180 L250 220 L180 230 L150 200 Z" />
                    {/* Europe */}
                    <path d="M480 140 L550 135 L570 170 L540 190 L480 185 Z" />
                    {/* Asia */}
                    <path d="M600 120 L800 110 L850 180 L780 200 L600 190 Z" />
                    {/* Africa */}
                    <path d="M480 200 L550 195 L570 280 L520 320 L480 300 Z" />
                    {/* Australia */}
                    <path d="M750 350 L850 345 L870 380 L820 390 L750 385 Z" />
                    {/* South America */}
                    <path d="M250 250 L320 245 L340 350 L280 380 L250 320 Z" />
                  </g>

                  {/* Connection lines */}
                  <g stroke="#4338ca" strokeWidth="2" fill="none" opacity="0.6">
                    <path d="M200 180 Q400 ***********" />
                    <path d="M500 160 Q650 ***********" />
                    <path d="M750 150 Q800 ***********" />
                    <path d="M500 160 Q400 ***********" />
                  </g>

                  {/* Location pins */}
                  <g>
                    <circle cx="200" cy="180" r="8" fill="#4338ca" className="animate-pulse" />
                    <circle cx="500" cy="160" r="8" fill="#4338ca" className="animate-pulse" style={{ animationDelay: '0.5s' }} />
                    <circle cx="750" cy="150" r="8" fill="#4338ca" className="animate-pulse" style={{ animationDelay: '1s' }} />
                    <circle cx="800" cy="370" r="8" fill="#4338ca" className="animate-pulse" style={{ animationDelay: '1.5s' }} />
                    <circle cx="520" cy="280" r="8" fill="#4338ca" className="animate-pulse" style={{ animationDelay: '2s' }} />
                  </g>
                </svg>

                {/* Floating stats */}
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-md">
                  <div className="text-2xl font-bold text-navy-600">120+</div>
                  <div className="text-sm text-gray-600">Countries</div>
                </div>

                <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-md">
                  <div className="text-2xl font-bold text-navy-600">50K+</div>
                  <div className="text-sm text-gray-600">Moves</div>
                </div>
              </div>
            </div>
          </div>

          {/* Trust Indicators - Full Width */}
          <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-12 animate-fade-in-up text-center" style={{ animationDelay: '1s' }}>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-navy-600 mb-3">15+</div>
              <div className="text-muted text-lg">Years Experience</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-navy-600 mb-3">50K+</div>
              <div className="text-muted text-lg">Successful Moves</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-navy-600 mb-3">120+</div>
              <div className="text-muted text-lg">Countries Served</div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Quick Quote Section */}
      <section className="section bg-minimalist-alt">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl heading-main mb-6">
              Get Your Moving Quote in Minutes
            </h2>
            <p className="text-xl text-muted">
              Tell us about your move and get an instant estimate
            </p>
          </div>

          <QuoteCalculator />
        </div>
      </section>

      {/* Services Carousel */}
      <ServicesCarousel />

      {/* World Map */}
      <WorldMap />

      {/* Testimonials */}
      <Testimonials />
    </Layout>
  );
};

export default HomePage;
