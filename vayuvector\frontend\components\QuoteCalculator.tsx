import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowRightIcon, CalculatorIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface QuoteData {
  origin: {
    country: string;
    city: string;
  };
  destination: {
    country: string;
    city: string;
  };
  homeSize: string;
  moveDate: string;
  packingService: boolean;
  storageNeeded: boolean;
  storageDuration: number;
}

interface QuoteResult {
  basePrice: number;
  packingPrice: number;
  storagePrice: number;
  totalPrice: number;
  currency: string;
  breakdown: {
    homeSize: string;
    isInternational: boolean;
    packingService: boolean;
    storageNeeded: boolean;
  };
}

const QuoteCalculator: React.FC = () => {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<QuoteResult | null>(null);
  const [formData, setFormData] = useState<QuoteData>({
    origin: { country: '', city: '' },
    destination: { country: '', city: '' },
    homeSize: '',
    moveDate: '',
    packingService: false,
    storageNeeded: false,
    storageDuration: 0,
  });

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof QuoteData] as object || {}),
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const calculateQuote = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/quotes/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          origin: formData.origin,
          destination: formData.destination,
          homeSize: formData.homeSize,
          packingService: formData.packingService,
          storageNeeded: formData.storageNeeded,
          storageDuration: formData.storageDuration,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to calculate quote');
      }

      const data = await response.json();
      setResult(data.data);
      setStep(4);
      toast.success('Quote calculated successfully!');
    } catch (error) {
      toast.error('Failed to calculate quote. Please try again.');
      console.error('Quote calculation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (step < 3) {
      setStep(step + 1);
    } else {
      calculateQuote();
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const resetCalculator = () => {
    setStep(1);
    setResult(null);
    setFormData({
      origin: { country: '', city: '' },
      destination: { country: '', city: '' },
      homeSize: '',
      moveDate: '',
      packingService: false,
      storageNeeded: false,
      storageDuration: 0,
    });
  };

  const isStepValid = () => {
    switch (step) {
      case 1:
        return formData.origin.country && formData.origin.city && 
               formData.destination.country && formData.destination.city;
      case 2:
        return formData.homeSize && formData.moveDate;
      case 3:
        return true; // Optional services
      default:
        return false;
    }
  };

  return (
    <div className="card p-8 max-w-2xl mx-auto">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-body">
            Step {step} of {result ? 4 : 3}
          </span>
          <span className="text-sm text-muted">
            {step === 1 && 'Locations'}
            {step === 2 && 'Move Details'}
            {step === 3 && 'Additional Services'}
            {step === 4 && 'Your Quote'}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-navy-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(step / (result ? 4 : 3)) * 100}%` }}
          ></div>
        </div>
      </div>

      <motion.div
        key={step}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
      >
        {/* Step 1: Locations */}
        {step === 1 && (
          <div className="space-y-6">
            <h3 className="text-xl heading-secondary mb-4">
              Where are you moving?
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-body mb-2">
                  Moving From
                </label>
                <input
                  type="text"
                  placeholder="Country"
                  value={formData.origin.country}
                  onChange={(e) => handleInputChange('origin.country', e.target.value)}
                  className="input mb-2"
                />
                <input
                  type="text"
                  placeholder="City"
                  value={formData.origin.city}
                  onChange={(e) => handleInputChange('origin.city', e.target.value)}
                  className="input"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-body mb-2">
                  Moving To
                </label>
                <input
                  type="text"
                  placeholder="Country"
                  value={formData.destination.country}
                  onChange={(e) => handleInputChange('destination.country', e.target.value)}
                  className="input mb-2"
                />
                <input
                  type="text"
                  placeholder="City"
                  value={formData.destination.city}
                  onChange={(e) => handleInputChange('destination.city', e.target.value)}
                  className="input"
                />
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Move Details */}
        {step === 2 && (
          <div className="space-y-6">
            <h3 className="text-xl heading-secondary mb-4">
              Tell us about your move
            </h3>

            <div>
              <label className="block text-sm font-medium text-body mb-2">
                Home Size
              </label>
              <select
                value={formData.homeSize}
                onChange={(e) => handleInputChange('homeSize', e.target.value)}
                className="input"
              >
                <option value="">Select home size</option>
                <option value="studio">Studio</option>
                <option value="1-bedroom">1 Bedroom</option>
                <option value="2-bedroom">2 Bedroom</option>
                <option value="3-bedroom">3 Bedroom</option>
                <option value="4-bedroom">4 Bedroom</option>
                <option value="5-bedroom">5+ Bedroom</option>
                <option value="office">Office</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-body mb-2">
                Preferred Move Date
              </label>
              <input
                type="date"
                value={formData.moveDate}
                onChange={(e) => handleInputChange('moveDate', e.target.value)}
                className="input"
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>
        )}

        {/* Step 3: Additional Services */}
        {step === 3 && (
          <div className="space-y-6">
            <h3 className="text-xl heading-secondary mb-4">
              Additional Services
            </h3>

            <div className="space-y-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.packingService}
                  onChange={(e) => handleInputChange('packingService', e.target.checked)}
                  className="rounded border-gray-300 text-navy-600 focus:ring-navy-500"
                />
                <span className="ml-2 text-body">
                  Professional packing service (+30% of base cost)
                </span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.storageNeeded}
                  onChange={(e) => handleInputChange('storageNeeded', e.target.checked)}
                  className="rounded border-gray-300 text-navy-600 focus:ring-navy-500"
                />
                <span className="ml-2 text-body">
                  Storage service needed
                </span>
              </label>
              
              {formData.storageNeeded && (
                <div className="ml-6">
                  <label className="block text-sm font-medium text-body mb-2">
                    Storage Duration (months)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="24"
                    value={formData.storageDuration}
                    onChange={(e) => handleInputChange('storageDuration', parseInt(e.target.value) || 0)}
                    className="input w-32"
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Step 4: Results */}
        {step === 4 && result && (
          <div className="space-y-6">
            <div className="text-center">
              <CalculatorIcon className="h-12 w-12 text-navy-600 mx-auto mb-4" />
              <h3 className="text-2xl heading-main mb-2">
                Your Estimated Quote
              </h3>
              <p className="text-muted">
                From {formData.origin.city}, {formData.origin.country} to{' '}
                {formData.destination.city}, {formData.destination.country}
              </p>
            </div>

            <div className="bg-minimalist-alt rounded-lg p-6">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted">Base moving cost:</span>
                  <span className="font-medium text-body">${result.basePrice.toLocaleString()}</span>
                </div>
                {result.packingPrice > 0 && (
                  <div className="flex justify-between">
                    <span className="text-muted">Packing service:</span>
                    <span className="font-medium text-body">${result.packingPrice.toLocaleString()}</span>
                  </div>
                )}
                {result.storagePrice > 0 && (
                  <div className="flex justify-between">
                    <span className="text-muted">Storage service:</span>
                    <span className="font-medium text-body">${result.storagePrice.toLocaleString()}</span>
                  </div>
                )}
                <hr className="border-gray-300" />
                <div className="flex justify-between text-lg font-bold">
                  <span className="text-body">Total Estimated Cost:</span>
                  <span className="text-navy-600">${result.totalPrice.toLocaleString()}</span>
                </div>
              </div>
            </div>
            
            <div className="text-center space-y-4">
              <p className="text-sm text-gray-500">
                This is an estimate. Final pricing may vary based on actual inventory and services.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button className="btn-primary">
                  Get Detailed Quote
                </button>
                <button
                  onClick={resetCalculator}
                  className="btn-outline"
                >
                  Calculate Again
                </button>
              </div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Navigation Buttons */}
      {step < 4 && (
        <div className="flex justify-between mt-8">
          <button
            onClick={prevStep}
            disabled={step === 1}
            className="btn-ghost disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <button
            onClick={nextStep}
            disabled={!isStepValid() || loading}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {loading ? (
              <div className="spinner"></div>
            ) : (
              <>
                <span>{step === 3 ? 'Calculate Quote' : 'Next'}</span>
                <ArrowRightIcon className="h-4 w-4" />
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default QuoteCalculator;
