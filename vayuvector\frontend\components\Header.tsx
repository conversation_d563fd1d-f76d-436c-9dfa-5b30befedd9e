import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { Bars3Icon, XMarkIcon, PhoneIcon } from '@heroicons/react/24/outline';
import { ChevronDownIcon } from '@heroicons/react/20/solid';

const navigation = [
  { name: 'Home', href: '/' },
  {
    name: 'Services',
    href: '/services',
    children: [
      { name: 'Residential Relocation', href: '/services/residential' },
      { name: 'Vehicle Transportation', href: '/services/vehicle' },
      { name: 'Documentation Support', href: '/services/documentation' },
      { name: 'Storage Solutions', href: '/services/storage' },
    ],
  },
  { name: 'How It Works', href: '/how-it-works' },
  { name: 'About', href: '/about' },
  { name: 'Contact', href: '/contact' },
];

const Header: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [servicesOpen, setServicesOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled
          ? 'bg-white shadow-sm border-b border-gray-200'
          : 'bg-white/95 backdrop-blur-sm'
      }`}
    >
      <nav className="container-custom">
        <div className="flex items-center justify-between h-20 lg:h-24">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/logo-new.svg"
                alt="VayuVector Logo"
                width={400}
                height={120}
                className="h-16 lg:h-20 w-auto"
                priority
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            {navigation.map((item) => (
              <div key={item.name} className="relative">
                {item.children ? (
                  <div
                    className="relative"
                    onMouseEnter={() => setServicesOpen(true)}
                    onMouseLeave={() => setServicesOpen(false)}
                  >
                    <button
                      className={`flex items-center space-x-1 px-4 py-3 text-base font-medium transition-colors duration-200 ${
                        isActive(item.href)
                          ? 'text-navy-700'
                          : 'text-black hover:text-navy-700'
                      }`}
                    >
                      <span>{item.name}</span>
                      <ChevronDownIcon
                        className={`h-4 w-4 transition-transform duration-200 ${
                          servicesOpen ? 'rotate-180' : ''
                        }`}
                      />
                    </button>

                    {/* Dropdown Menu */}
                    {servicesOpen && (
                      <div className="absolute top-full left-0 mt-2 w-72 bg-white rounded-lg shadow-xl border border-gray-200 py-3 z-50">
                        {item.children.map((child) => (
                          <Link
                            key={child.name}
                            href={child.href}
                            className="block px-6 py-3 text-base text-black hover:bg-navy-50 hover:text-navy-700 transition-colors duration-200 border-b border-gray-100 last:border-b-0"
                          >
                            <div className="font-medium">{child.name}</div>
                            <div className="text-sm text-gray-500 mt-1">
                              {child.name === 'Residential Relocation' && 'Complete household moving services'}
                              {child.name === 'Vehicle Transportation' && 'Safe transport for your vehicles'}
                              {child.name === 'Documentation Support' && 'Visa and paperwork assistance'}
                              {child.name === 'Storage Solutions' && 'Secure storage facilities'}
                            </div>
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={`px-4 py-3 text-base font-medium transition-colors duration-200 ${
                      isActive(item.href)
                        ? 'text-navy-700'
                        : 'text-black hover:text-navy-700'
                    }`}
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link
              href="/quote"
              className="bg-navy-700 text-white font-semibold text-base px-8 py-3 rounded-lg hover:bg-navy-800 transition-colors duration-200"
            >
              Get Quote
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              type="button"
              className={`p-2 rounded-md transition-colors duration-200 ${
                scrolled
                  ? 'text-gray-700 hover:text-secondary-600'
                  : 'text-white hover:text-secondary-200'
              }`}
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white rounded-lg shadow-medium mt-2">
              {navigation.map((item) => (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className={`block px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 ${
                      isActive(item.href)
                        ? 'text-navy-700 bg-navy-50'
                        : 'text-black hover:text-navy-700 hover:bg-gray-50'
                    }`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                  {item.children && (
                    <div className="ml-4 space-y-2 mt-2">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="block px-4 py-3 text-base text-gray-700 hover:text-navy-700 hover:bg-navy-50 rounded-lg transition-colors duration-200 border border-gray-100"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          <div className="font-medium">{child.name}</div>
                          <div className="text-sm text-gray-500 mt-1">
                            {child.name === 'Residential Relocation' && 'Complete household moving'}
                            {child.name === 'Vehicle Transportation' && 'Safe vehicle transport'}
                            {child.name === 'Documentation Support' && 'Visa & paperwork help'}
                            {child.name === 'Storage Solutions' && 'Secure storage facilities'}
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              
              {/* Mobile CTA */}
              <div className="pt-4 border-t border-gray-200">
                <Link
                  href="/quote"
                  className="block w-full bg-navy-700 text-white text-center font-semibold px-4 py-2 rounded-lg hover:bg-navy-800 transition-colors duration-200"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Get Quote
                </Link>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
};

export default Header;
