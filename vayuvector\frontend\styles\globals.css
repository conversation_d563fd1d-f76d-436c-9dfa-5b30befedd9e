@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-black bg-white text-lg;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* Focus styles */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-accent {
    @apply btn bg-accent-600 text-white hover:bg-accent-700 focus:ring-accent-500;
  }
  
  .btn-outline {
    @apply btn border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-600 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100;
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
  }
  
  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }
  
  .input-error {
    @apply input border-red-300 focus:ring-red-500 focus:border-red-500;
  }
  
  /* Container styles */
  .container-custom {
    @apply max-w-none mx-auto px-6 sm:px-8 lg:px-12 xl:px-16;
  }

  .container-narrow {
    @apply max-w-7xl mx-auto px-6 sm:px-8 lg:px-12;
  }
  
  /* Section styles */
  .section {
    @apply py-20 lg:py-32;
  }

  .section-sm {
    @apply py-12 lg:py-16;
  }
  
  /* Text styles */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent-600 to-primary-600 bg-clip-text text-transparent;
  }

  /* Minimalistic Design Classes */
  .heading-main {
    @apply text-navy-700 font-bold text-lg;
  }

  .heading-secondary {
    @apply text-navy-600 font-semibold text-base;
  }

  .text-body {
    @apply text-black text-base;
  }

  .text-muted {
    @apply text-gray-600 text-base;
  }

  .bg-minimalist {
    @apply bg-white;
  }

  .bg-minimalist-alt {
    @apply bg-gray-50;
  }

  /* List styles with navy bullets */
  .list-navy {
    @apply list-disc list-inside;
  }

  .list-navy li {
    @apply text-black;
  }

  .list-navy li::marker {
    @apply text-navy-600;
  }
  
  /* Animation utilities */
  .animate-on-scroll {
    @apply opacity-0 translate-y-8 transition-all duration-700;
  }
  
  .animate-on-scroll.in-view {
    @apply opacity-100 translate-y-0;
  }
  
  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }
  
  /* Backdrop blur */
  .backdrop-blur-custom {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

/* Utility styles */
@layer utilities {
  /* Custom shadows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  }

  .shadow-glow-accent {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
  }
  
  /* Custom gradients */
  .bg-gradient-brand {
    background: linear-gradient(135deg, #000000 0%, #404040 50%, #FFD700 100%);
  }
  
  .bg-gradient-soft {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }
  
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Aspect ratios */
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  
  /* Grid utilities */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .dark-mode-auto {
    @apply bg-gray-900 text-white;
  }
}
