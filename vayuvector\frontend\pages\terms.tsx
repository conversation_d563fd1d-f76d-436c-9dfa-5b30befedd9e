import React from 'react';
import Layout from '@/components/Layout';
import { DocumentTextIcon, ScaleIcon, ExclamationTriangleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

const TermsOfServicePage: React.FC = () => {
  const lastUpdated = "December 1, 2024";

  const sections = [
    {
      title: "Service Agreement",
      icon: DocumentTextIcon,
      content: [
        "VayuVector provides international relocation and moving services",
        "Services are subject to availability and scheduling",
        "All quotes are estimates and may vary based on actual requirements",
        "Additional services may incur extra charges",
        "Service modifications must be agreed upon in writing"
      ]
    },
    {
      title: "Customer Responsibilities",
      icon: CheckCircleIcon,
      content: [
        "Provide accurate and complete information about your move",
        "Ensure all items are properly prepared for transport",
        "Comply with customs and immigration requirements",
        "Make timely payments as per agreed terms",
        "Notify us of any changes to your moving requirements",
        "Be available for scheduled pickups and deliveries"
      ]
    },
    {
      title: "Liability and Insurance",
      icon: ExclamationTriangleIcon,
      content: [
        "VayuVector maintains comprehensive insurance coverage",
        "Liability is limited to the declared value of items",
        "Customers are responsible for declaring high-value items",
        "Claims must be reported within specified timeframes",
        "Some items may be excluded from coverage",
        "Additional insurance options are available"
      ]
    },
    {
      title: "Payment Terms",
      icon: ScaleIcon,
      content: [
        "Payment schedules are outlined in service agreements",
        "Deposits may be required to secure services",
        "Final payment is due before delivery",
        "Late payments may incur additional fees",
        "Refunds are subject to cancellation policies",
        "All prices are subject to applicable taxes"
      ]
    }
  ];

  return (
    <Layout
      title="Terms of Service - VayuVector"
      description="VayuVector's terms of service outline the conditions for using our international relocation and moving services."
    >
      {/* Hero Section */}
      <section className="pt-16 pb-16 bg-gradient-to-br from-gray-50 to-secondary-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <ScaleIcon className="h-12 w-12 text-secondary-600" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                Terms of Service
              </h1>
            </div>
            <p className="text-lg md:text-xl text-gray-600 mb-4">
              These terms govern your use of VayuVector's services and establish 
              the rights and responsibilities of both parties.
            </p>
            <p className="text-sm text-gray-500">
              Last updated: {lastUpdated}
            </p>
          </div>
        </div>
      </section>

      {/* Introduction */}
      <section className="section">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="card p-8 mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Agreement Overview</h2>
              <p className="text-gray-600 mb-4">
                These Terms of Service ("Terms") constitute a legally binding agreement between 
                you ("Customer") and VayuVector ("Company") regarding the use of our international 
                relocation and moving services.
              </p>
              <p className="text-gray-600">
                By engaging our services, you acknowledge that you have read, understood, and 
                agree to be bound by these Terms. If you do not agree with these Terms, 
                please do not use our services.
              </p>
            </div>

            {/* Terms Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => {
                const IconComponent = section.icon;
                return (
                  <div key={index} className="card p-8">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="w-12 h-12 bg-navy-100 rounded-lg flex items-center justify-center">
                        <IconComponent className="h-6 w-6 text-navy-600" />
                      </div>
                      <h2 className="text-2xl heading-main">{section.title}</h2>
                    </div>
                    <ul className="space-y-3 list-navy">
                      {section.content.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-navy-600 rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-body">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              })}
            </div>

            {/* Additional Terms */}
            <div className="space-y-8 mt-12">
              <div className="card p-8">
                <h2 className="text-2xl heading-main mb-4">Cancellation Policy</h2>
                <div className="space-y-4 text-body">
                  <p><strong className="text-navy-600">More than 30 days before move:</strong> Full refund minus processing fees</p>
                  <p><strong className="text-navy-600">15-30 days before move:</strong> 75% refund</p>
                  <p><strong className="text-navy-600">7-14 days before move:</strong> 50% refund</p>
                  <p><strong className="text-navy-600">Less than 7 days before move:</strong> 25% refund</p>
                  <p><strong className="text-navy-600">Same day cancellation:</strong> No refund</p>
                </div>
              </div>

              <div className="card p-8">
                <h2 className="text-2xl heading-main mb-4">Force Majeure</h2>
                <p className="text-muted mb-4">
                  VayuVector shall not be liable for any delay or failure to perform due to 
                  circumstances beyond our reasonable control, including but not limited to:
                </p>
                <ul className="space-y-2 text-gray-700">
                  <li>• Natural disasters, weather conditions</li>
                  <li>• Government actions, regulations, or restrictions</li>
                  <li>• Labor disputes or strikes</li>
                  <li>• Transportation delays or disruptions</li>
                  <li>• Acts of terrorism or war</li>
                </ul>
              </div>

              <div className="card p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Prohibited Items</h2>
                <p className="text-gray-600 mb-4">
                  The following items cannot be transported and must be disposed of or 
                  transported separately by the customer:
                </p>
                <ul className="space-y-2 text-gray-700">
                  <li>• Hazardous materials and chemicals</li>
                  <li>• Flammable liquids and gases</li>
                  <li>• Firearms and ammunition</li>
                  <li>• Perishable food items</li>
                  <li>• Live plants and animals</li>
                  <li>• Illegal substances</li>
                </ul>
              </div>

              <div className="card p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Dispute Resolution</h2>
                <p className="text-gray-600 mb-4">
                  Any disputes arising from these Terms or our services shall be resolved through:
                </p>
                <ol className="space-y-2 text-gray-700">
                  <li>1. Direct negotiation between the parties</li>
                  <li>2. Mediation by a neutral third party</li>
                  <li>3. Binding arbitration if mediation fails</li>
                </ol>
              </div>

              <div className="card p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Modifications</h2>
                <p className="text-gray-600">
                  VayuVector reserves the right to modify these Terms at any time. 
                  Changes will be effective immediately upon posting on our website. 
                  Continued use of our services constitutes acceptance of modified Terms.
                </p>
              </div>

              <div className="card p-8 bg-secondary-50 border-secondary-200">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Information</h2>
                <p className="text-gray-600 mb-4">
                  For questions about these Terms of Service, please contact us:
                </p>
                <div className="space-y-2 text-gray-700">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Address:</strong> 2441 Saki Naka, Andheri East, Mumbai, India</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default TermsOfServicePage;
