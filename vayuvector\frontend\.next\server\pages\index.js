/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchiveBoxIcon: () => (/* reexport safe */ _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   DocumentTextIcon: () => (/* reexport safe */ _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   GlobeAltIcon: () => (/* reexport safe */ _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   ShieldCheckIcon: () => (/* reexport safe */ _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   TruckIcon: () => (/* reexport safe */ _TruckIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArchiveBoxIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DocumentTextIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _GlobeAltIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GlobeAltIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ShieldCheckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _TruckIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TruckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcmNoaXZlQm94SWNvbixEb2N1bWVudFRleHRJY29uLEdsb2JlQWx0SWNvbixIb21lSWNvbixTaGllbGRDaGVja0ljb24sVHJ1Y2tJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQytEO0FBQ0k7QUFDUjtBQUNSO0FBQ2MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92YXl1dmVjdG9yLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/NTY4NyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJjaGl2ZUJveEljb24gfSBmcm9tIFwiLi9BcmNoaXZlQm94SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvY3VtZW50VGV4dEljb24gfSBmcm9tIFwiLi9Eb2N1bWVudFRleHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmVBbHRJY29uIH0gZnJvbSBcIi4vR2xvYmVBbHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZUljb24gfSBmcm9tIFwiLi9Ib21lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNoaWVsZENoZWNrSWNvbiB9IGZyb20gXCIuL1NoaWVsZENoZWNrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRydWNrSWNvbiB9IGZyb20gXCIuL1RydWNrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightIcon: () => (/* reexport safe */ _ArrowRightIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalculatorIcon: () => (/* reexport safe */ _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalculatorIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalculatorIcon.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0SWNvbixDYWxjdWxhdG9ySWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDK0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92YXl1dmVjdG9yLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ZmFjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dSaWdodEljb24gfSBmcm9tIFwiLi9BcnJvd1JpZ2h0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhbGN1bGF0b3JJY29uIH0gZnJvbSBcIi4vQ2FsY3VsYXRvckljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Bars3Icon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJzM0ljb24sWE1hcmtJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNxRCIsInNvdXJjZXMiOlsid2VicGFjazovL3ZheXV2ZWN0b3ItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz8zMzRkIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJzM0ljb24gfSBmcm9tIFwiLi9CYXJzM0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYTWFya0ljb24gfSBmcm9tIFwiLi9YTWFya0ljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bars3Icon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js":
/*!*********************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChevronDownIcon: () => (/* reexport safe */ _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChevronDownIcon.js */ "./node_modules/@heroicons/react/20/solid/esm/ChevronDownIcon.js");



/***/ }),

/***/ "__barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js":
/*!***********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronLeftIcon: () => (/* reexport safe */ _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronRightIcon: () => (/* reexport safe */ _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   StarIcon: () => (/* reexport safe */ _StarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChevronLeftIcon.js */ \"./node_modules/@heroicons/react/24/solid/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ChevronRightIcon.js */ \"./node_modules/@heroicons/react/24/solid/esm/ChevronRightIcon.js\");\n/* harmony import */ var _StarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./StarIcon.js */ \"./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGV2cm9uTGVmdEljb24sQ2hldnJvblJpZ2h0SWNvbixTdGFySWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvc29saWQvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDaUU7QUFDRSIsInNvdXJjZXMiOlsid2VicGFjazovL3ZheXV2ZWN0b3ItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9zb2xpZC9lc20vaW5kZXguanM/MjFmZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvbkxlZnRJY29uIH0gZnJvbSBcIi4vQ2hldnJvbkxlZnRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvblJpZ2h0SWNvbiB9IGZyb20gXCIuL0NoZXZyb25SaWdodEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdGFySWNvbiB9IGZyb20gXCIuL1N0YXJJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClockIcon: () => (/* reexport safe */ _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EnvelopeIcon: () => (/* reexport safe */ _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MapPinIcon: () => (/* reexport safe */ _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ClockIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ClockIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EnvelopeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MapPinIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DbG9ja0ljb24sRW52ZWxvcGVJY29uLE1hcFBpbkljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDcUQ7QUFDTSIsInNvdXJjZXMiOlsid2VicGFjazovL3ZheXV2ZWN0b3ItZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz82YTEyIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDbG9ja0ljb24gfSBmcm9tIFwiLi9DbG9ja0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFbnZlbG9wZUljb24gfSBmcm9tIFwiLi9FbnZlbG9wZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYXBQaW5JY29uIH0gZnJvbSBcIi4vTWFwUGluSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        services: [\n            {\n                name: \"Residential Relocation\",\n                href: \"/services/residential\"\n            },\n            {\n                name: \"Vehicle Transportation\",\n                href: \"/services/vehicle\"\n            },\n            {\n                name: \"Documentation Support\",\n                href: \"/services/documentation\"\n            },\n            {\n                name: \"Storage Solutions\",\n                href: \"/services/storage\"\n            }\n        ],\n        company: [\n            {\n                name: \"About Us\",\n                href: \"/about\"\n            },\n            {\n                name: \"How It Works\",\n                href: \"/how-it-works\"\n            },\n            {\n                name: \"Careers\",\n                href: \"/careers\"\n            }\n        ],\n        support: [\n            {\n                name: \"Contact Us\",\n                href: \"/contact\"\n            },\n            {\n                name: \"Customer Portal\",\n                href: \"/portal\"\n            }\n        ],\n        legal: [\n            {\n                name: \"Privacy Policy\",\n                href: \"/privacy\"\n            },\n            {\n                name: \"Terms of Service\",\n                href: \"/terms\"\n            },\n            {\n                name: \"Insurance\",\n                href: \"/insurance\"\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-12 lg:py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            src: \"/logo-dark.svg\",\n                                            alt: \"VayuVector Logo\",\n                                            width: 160,\n                                            height: 48,\n                                            className: \"h-8 w-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 max-w-sm\",\n                                        children: \"Your trusted global relocation partner. Moving lives, not just belongings, with comprehensive door-to-door services worldwide.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.EnvelopeIcon, {\n                                                        className: \"h-5 w-5 text-navy-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"text-gray-300 hover:text-white transition-colors\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.MapPinIcon, {\n                                                        className: \"h-5 w-5 text-navy-400 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: [\n                                                            \"2441 Saki Naka, Andheri East\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 71,\n                                                                columnNumber: 47\n                                                            }, undefined),\n                                                            \"Mumbai, India\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ClockIcon, {\n                                                        className: \"h-5 w-5 text-navy-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"24/7 Customer Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.services.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.company.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Legal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-8 border-t border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Stay Updated\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4\",\n                                    children: \"Get relocation tips and updates delivered to your inbox.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"btn-primary whitespace-nowrap\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" VayuVector. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Footer.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0Zvb3Rlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDRztBQUNFO0FBTU07QUFFckMsTUFBTU0sU0FBbUI7SUFDdkIsTUFBTUMsY0FBYyxJQUFJQyxPQUFPQyxXQUFXO0lBRTFDLE1BQU1DLGNBQWM7UUFDbEJDLFVBQVU7WUFDUjtnQkFBRUMsTUFBTTtnQkFBMEJDLE1BQU07WUFBd0I7WUFDaEU7Z0JBQUVELE1BQU07Z0JBQTBCQyxNQUFNO1lBQW9CO1lBQzVEO2dCQUFFRCxNQUFNO2dCQUF5QkMsTUFBTTtZQUEwQjtZQUNqRTtnQkFBRUQsTUFBTTtnQkFBcUJDLE1BQU07WUFBb0I7U0FDeEQ7UUFDREMsU0FBUztZQUNQO2dCQUFFRixNQUFNO2dCQUFZQyxNQUFNO1lBQVM7WUFDbkM7Z0JBQUVELE1BQU07Z0JBQWdCQyxNQUFNO1lBQWdCO1lBQzlDO2dCQUFFRCxNQUFNO2dCQUFXQyxNQUFNO1lBQVc7U0FDckM7UUFDREUsU0FBUztZQUNQO2dCQUFFSCxNQUFNO2dCQUFjQyxNQUFNO1lBQVc7WUFDdkM7Z0JBQUVELE1BQU07Z0JBQW1CQyxNQUFNO1lBQVU7U0FDNUM7UUFDREcsT0FBTztZQUNMO2dCQUFFSixNQUFNO2dCQUFrQkMsTUFBTTtZQUFXO1lBQzNDO2dCQUFFRCxNQUFNO2dCQUFvQkMsTUFBTTtZQUFTO1lBQzNDO2dCQUFFRCxNQUFNO2dCQUFhQyxNQUFNO1lBQWE7U0FDekM7SUFDSDtJQUlBLHFCQUNFLDhEQUFDSTtRQUFPQyxXQUFVOzswQkFFaEIsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUViLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNqQixrREFBSUE7d0NBQUNZLE1BQUs7d0NBQUlLLFdBQVU7a0RBQ3ZCLDRFQUFDaEIsbURBQUtBOzRDQUNKa0IsS0FBSTs0Q0FDSkMsS0FBSTs0Q0FDSkMsT0FBTzs0Q0FDUEMsUUFBUTs0Q0FDUkwsV0FBVTs7Ozs7Ozs7Ozs7a0RBR2QsOERBQUNNO3dDQUFFTixXQUFVO2tEQUE4Qjs7Ozs7O2tEQU0zQyw4REFBQ0M7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDQztnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNkLDZIQUFZQTt3REFBQ2MsV0FBVTs7Ozs7O2tFQUN4Qiw4REFBQ087d0RBQUVaLE1BQUs7d0RBQTZCSyxXQUFVO2tFQUFtRDs7Ozs7Ozs7Ozs7OzBEQUlwRyw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDZiwySEFBVUE7d0RBQUNlLFdBQVU7Ozs7OztrRUFDdEIsOERBQUNRO3dEQUFLUixXQUFVOzs0REFBZ0I7MEVBQ0YsOERBQUNTOzs7Ozs0REFBSzs7Ozs7Ozs7Ozs7OzswREFJdEMsOERBQUNSO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ2IsMEhBQVNBO3dEQUFDYSxXQUFVOzs7Ozs7a0VBQ3JCLDhEQUFDUTt3REFBS1IsV0FBVTtrRUFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FRdEMsOERBQUNDOztrREFDQyw4REFBQ1M7d0NBQUdWLFdBQVU7a0RBQTZCOzs7Ozs7a0RBQzNDLDhEQUFDVzt3Q0FBR1gsV0FBVTtrREFDWFIsWUFBWUMsUUFBUSxDQUFDbUIsR0FBRyxDQUFDLENBQUNDLHFCQUN6Qiw4REFBQ0M7MERBQ0MsNEVBQUMvQixrREFBSUE7b0RBQ0hZLE1BQU1rQixLQUFLbEIsSUFBSTtvREFDZkssV0FBVTs4REFFVGEsS0FBS25CLElBQUk7Ozs7OzsrQ0FMTG1CLEtBQUtuQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzBDQWF4Qiw4REFBQ087O2tEQUNDLDhEQUFDUzt3Q0FBR1YsV0FBVTtrREFBNkI7Ozs7OztrREFDM0MsOERBQUNXO3dDQUFHWCxXQUFVO2tEQUNYUixZQUFZSSxPQUFPLENBQUNnQixHQUFHLENBQUMsQ0FBQ0MscUJBQ3hCLDhEQUFDQzswREFDQyw0RUFBQy9CLGtEQUFJQTtvREFDSFksTUFBTWtCLEtBQUtsQixJQUFJO29EQUNmSyxXQUFVOzhEQUVUYSxLQUFLbkIsSUFBSTs7Ozs7OytDQUxMbUIsS0FBS25CLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7MENBYXhCLDhEQUFDTzs7a0RBQ0MsOERBQUNTO3dDQUFHVixXQUFVO2tEQUE2Qjs7Ozs7O2tEQUMzQyw4REFBQ1c7d0NBQUdYLFdBQVU7a0RBQ1hSLFlBQVlLLE9BQU8sQ0FBQ2UsR0FBRyxDQUFDLENBQUNDLHFCQUN4Qiw4REFBQ0M7MERBQ0MsNEVBQUMvQixrREFBSUE7b0RBQ0hZLE1BQU1rQixLQUFLbEIsSUFBSTtvREFDZkssV0FBVTs4REFFVGEsS0FBS25CLElBQUk7Ozs7OzsrQ0FMTG1CLEtBQUtuQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzBDQWF4Qiw4REFBQ087O2tEQUNDLDhEQUFDUzt3Q0FBR1YsV0FBVTtrREFBNkI7Ozs7OztrREFDM0MsOERBQUNXO3dDQUFHWCxXQUFVO2tEQUNYUixZQUFZTSxLQUFLLENBQUNjLEdBQUcsQ0FBQyxDQUFDQyxxQkFDdEIsOERBQUNDOzBEQUNDLDRFQUFDL0Isa0RBQUlBO29EQUNIWSxNQUFNa0IsS0FBS2xCLElBQUk7b0RBQ2ZLLFdBQVU7OERBRVRhLEtBQUtuQixJQUFJOzs7Ozs7K0NBTExtQixLQUFLbkIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FjMUIsOERBQUNPO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNVO29DQUFHVixXQUFVOzhDQUE2Qjs7Ozs7OzhDQUMzQyw4REFBQ007b0NBQUVOLFdBQVU7OENBQXFCOzs7Ozs7OENBR2xDLDhEQUFDZTtvQ0FBS2YsV0FBVTs7c0RBQ2QsOERBQUNnQjs0Q0FDQ0MsTUFBSzs0Q0FDTEMsYUFBWTs0Q0FDWmxCLFdBQVU7Ozs7OztzREFFWiw4REFBQ21COzRDQUNDRixNQUFLOzRDQUNMakIsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1QsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVOztnQ0FBd0I7Z0NBQ2xDWDtnQ0FBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU83QjtBQUVBLGlFQUFlRCxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmF5dXZlY3Rvci1mcm9udGVuZC8uL2NvbXBvbmVudHMvRm9vdGVyLnRzeD9hNzlmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuaW1wb3J0IHsgXG4gIE1hcFBpbkljb24sIFxuICBQaG9uZUljb24sIFxuICBFbnZlbG9wZUljb24sXG4gIENsb2NrSWNvbiBcbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcblxuY29uc3QgRm9vdGVyOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgY3VycmVudFllYXIgPSBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCk7XG5cbiAgY29uc3QgZm9vdGVyTGlua3MgPSB7XG4gICAgc2VydmljZXM6IFtcbiAgICAgIHsgbmFtZTogJ1Jlc2lkZW50aWFsIFJlbG9jYXRpb24nLCBocmVmOiAnL3NlcnZpY2VzL3Jlc2lkZW50aWFsJyB9LFxuICAgICAgeyBuYW1lOiAnVmVoaWNsZSBUcmFuc3BvcnRhdGlvbicsIGhyZWY6ICcvc2VydmljZXMvdmVoaWNsZScgfSxcbiAgICAgIHsgbmFtZTogJ0RvY3VtZW50YXRpb24gU3VwcG9ydCcsIGhyZWY6ICcvc2VydmljZXMvZG9jdW1lbnRhdGlvbicgfSxcbiAgICAgIHsgbmFtZTogJ1N0b3JhZ2UgU29sdXRpb25zJywgaHJlZjogJy9zZXJ2aWNlcy9zdG9yYWdlJyB9LFxuICAgIF0sXG4gICAgY29tcGFueTogW1xuICAgICAgeyBuYW1lOiAnQWJvdXQgVXMnLCBocmVmOiAnL2Fib3V0JyB9LFxuICAgICAgeyBuYW1lOiAnSG93IEl0IFdvcmtzJywgaHJlZjogJy9ob3ctaXQtd29ya3MnIH0sXG4gICAgICB7IG5hbWU6ICdDYXJlZXJzJywgaHJlZjogJy9jYXJlZXJzJyB9LFxuICAgIF0sXG4gICAgc3VwcG9ydDogW1xuICAgICAgeyBuYW1lOiAnQ29udGFjdCBVcycsIGhyZWY6ICcvY29udGFjdCcgfSxcbiAgICAgIHsgbmFtZTogJ0N1c3RvbWVyIFBvcnRhbCcsIGhyZWY6ICcvcG9ydGFsJyB9LFxuICAgIF0sXG4gICAgbGVnYWw6IFtcbiAgICAgIHsgbmFtZTogJ1ByaXZhY3kgUG9saWN5JywgaHJlZjogJy9wcml2YWN5JyB9LFxuICAgICAgeyBuYW1lOiAnVGVybXMgb2YgU2VydmljZScsIGhyZWY6ICcvdGVybXMnIH0sXG4gICAgICB7IG5hbWU6ICdJbnN1cmFuY2UnLCBocmVmOiAnL2luc3VyYW5jZScgfSxcbiAgICBdLFxuICB9O1xuXG5cblxuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgey8qIE1haW4gRm9vdGVyIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1jdXN0b20gcHktMTIgbGc6cHktMTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy02IGdhcC04XCI+XG4gICAgICAgICAgey8qIENvbXBhbnkgSW5mbyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTRcIj5cbiAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgc3JjPVwiL2xvZ28tZGFyay5zdmdcIlxuICAgICAgICAgICAgICAgIGFsdD1cIlZheXVWZWN0b3IgTG9nb1wiXG4gICAgICAgICAgICAgICAgd2lkdGg9ezE2MH1cbiAgICAgICAgICAgICAgICBoZWlnaHQ9ezQ4fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LWF1dG9cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBtYi02IG1heC13LXNtXCI+XG4gICAgICAgICAgICAgIFlvdXIgdHJ1c3RlZCBnbG9iYWwgcmVsb2NhdGlvbiBwYXJ0bmVyLiBNb3ZpbmcgbGl2ZXMsIG5vdCBqdXN0IGJlbG9uZ2luZ3MsIFxuICAgICAgICAgICAgICB3aXRoIGNvbXByZWhlbnNpdmUgZG9vci10by1kb29yIHNlcnZpY2VzIHdvcmxkd2lkZS5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIENvbnRhY3QgSW5mbyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPEVudmVsb3BlSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtbmF2eS00MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCJtYWlsdG86aW5mb0B2YXl1dmVjdG9yLmNvbVwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgaW5mb0B2YXl1dmVjdG9yLmNvbVxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8TWFwUGluSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtbmF2eS00MDAgbXQtMC41XCIgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAyNDQxIFNha2kgTmFrYSwgQW5kaGVyaSBFYXN0PGJyIC8+XG4gICAgICAgICAgICAgICAgICBNdW1iYWksIEluZGlhXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8Q2xvY2tJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1uYXZ5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgMjQvNyBDdXN0b21lciBTdXBwb3J0XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFNlcnZpY2VzICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5TZXJ2aWNlczwvaDM+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtmb290ZXJMaW5rcy5zZXJ2aWNlcy5tYXAoKGxpbmspID0+IChcbiAgICAgICAgICAgICAgICA8bGkga2V5PXtsaW5rLm5hbWV9PlxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgaHJlZj17bGluay5ocmVmfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2xpbmsubmFtZX1cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQ29tcGFueSAqL31cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+Q29tcGFueTwvaDM+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtmb290ZXJMaW5rcy5jb21wYW55Lm1hcCgobGluaykgPT4gKFxuICAgICAgICAgICAgICAgIDxsaSBrZXk9e2xpbmsubmFtZX0+XG4gICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICBocmVmPXtsaW5rLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7bGluay5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBTdXBwb3J0ICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5TdXBwb3J0PC9oMz5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge2Zvb3RlckxpbmtzLnN1cHBvcnQubWFwKChsaW5rKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17bGluay5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2xpbmsuaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtsaW5rLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIExlZ2FsICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5MZWdhbDwvaDM+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtmb290ZXJMaW5rcy5sZWdhbC5tYXAoKGxpbmspID0+IChcbiAgICAgICAgICAgICAgICA8bGkga2V5PXtsaW5rLm5hbWV9PlxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgaHJlZj17bGluay5ocmVmfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2xpbmsubmFtZX1cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBOZXdzbGV0dGVyIFNpZ251cCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xMiBwdC04IGJvcmRlci10IGJvcmRlci1ncmF5LTgwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctbWRcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMlwiPlN0YXkgVXBkYXRlZDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgR2V0IHJlbG9jYXRpb24gdGlwcyBhbmQgdXBkYXRlcyBkZWxpdmVyZWQgdG8geW91ciBpbmJveC5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxmb3JtIGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGVtYWlsXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHgtNCBweS0yIGJnLWdyYXktODAwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LTUwMCBmb2N1czpib3JkZXItcHJpbWFyeS01MDBcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnkgd2hpdGVzcGFjZS1ub3dyYXBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgU3Vic2NyaWJlXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQm90dG9tIEJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYm9yZGVyLWdyYXktODAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLWN1c3RvbSBweS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgwqkge2N1cnJlbnRZZWFyfSBWYXl1VmVjdG9yLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9mb290ZXI+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBGb290ZXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMaW5rIiwiSW1hZ2UiLCJNYXBQaW5JY29uIiwiRW52ZWxvcGVJY29uIiwiQ2xvY2tJY29uIiwiRm9vdGVyIiwiY3VycmVudFllYXIiLCJEYXRlIiwiZ2V0RnVsbFllYXIiLCJmb290ZXJMaW5rcyIsInNlcnZpY2VzIiwibmFtZSIsImhyZWYiLCJjb21wYW55Iiwic3VwcG9ydCIsImxlZ2FsIiwiZm9vdGVyIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJwIiwiYSIsInNwYW4iLCJiciIsImgzIiwidWwiLCJtYXAiLCJsaW5rIiwibGkiLCJmb3JtIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJidXR0b24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/Footer.tsx\n");

/***/ }),

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/20/solid */ \"__barrel_optimize__?names=ChevronDownIcon!=!./node_modules/@heroicons/react/20/solid/esm/index.js\");\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"Services\",\n        href: \"/services\",\n        children: [\n            {\n                name: \"Residential Relocation\",\n                href: \"/services/residential\"\n            },\n            {\n                name: \"Vehicle Transportation\",\n                href: \"/services/vehicle\"\n            },\n            {\n                name: \"Documentation Support\",\n                href: \"/services/documentation\"\n            },\n            {\n                name: \"Storage Solutions\",\n                href: \"/services/storage\"\n            }\n        ]\n    },\n    {\n        name: \"How It Works\",\n        href: \"/how-it-works\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    }\n];\nconst Header = ()=>{\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [servicesOpen, setServicesOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const isScrolled = window.scrollY > 10;\n            setScrolled(isScrolled);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? \"bg-white shadow-sm border-b border-gray-200\" : \"bg-white/95 backdrop-blur-sm\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-18\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    src: \"/logo-new.svg\",\n                                    alt: \"VayuVector Logo\",\n                                    width: 320,\n                                    height: 100,\n                                    className: \"h-10 lg:h-12 w-auto\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-2\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setServicesOpen(true),\n                                        onMouseLeave: ()=>setServicesOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-navy-700\" : \"text-black hover:text-navy-700\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_20_solid__WEBPACK_IMPORTED_MODULE_5__.ChevronDownIcon, {\n                                                        className: `h-4 w-4 transition-transform duration-200 ${servicesOpen ? \"rotate-180\" : \"\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            servicesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-black hover:bg-gray-50 hover:text-navy-700 transition-colors duration-200\",\n                                                        children: child.name\n                                                    }, child.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `px-3 py-2 text-sm font-medium transition-colors duration-200 ${isActive(item.href) ? \"text-navy-700\" : \"text-black hover:text-navy-700\"}`,\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/quote\",\n                                className: \"bg-navy-700 text-white font-medium px-6 py-2 rounded-md hover:bg-navy-800 transition-colors duration-200\",\n                                children: \"Get Quote\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: `p-2 rounded-md transition-colors duration-200 ${scrolled ? \"text-gray-700 hover:text-secondary-600\" : \"text-white hover:text-secondary-200\"}`,\n                                onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                                        className: \"h-6 w-6\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 bg-white rounded-lg shadow-medium mt-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: `block px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 ${isActive(item.href) ? \"text-navy-700 bg-navy-50\" : \"text-black hover:text-navy-700 hover:bg-gray-50\"}`,\n                                            onClick: ()=>setMobileMenuOpen(false),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 space-y-1\",\n                                            children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: child.href,\n                                                    className: \"block px-3 py-2 text-sm text-gray-600 hover:text-navy-700 hover:bg-gray-50 rounded-md transition-colors duration-200\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: child.name\n                                                }, child.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/quote\",\n                                    className: \"block w-full bg-navy-700 text-white text-center font-semibold px-4 py-2 rounded-lg hover:bg-navy-800 transition-colors duration-200\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: \"Get Quote\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Header.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.tsx\n");

/***/ }),

/***/ "./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"./components/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Footer */ \"./components/Footer.tsx\");\n\n\n\n\n\nconst Layout = ({ children, title = \"VayuVector - Your Global Relocation Partner\", description = \"Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support.\", keywords = \"international moving, relocation services, global shipping, door-to-door moving, expat services, international logistics\", ogImage = \"/images/og-image.jpg\", noIndex = false })=>{\n    const fullTitle = title.includes(\"VayuVector\") ? title : `${title} | VayuVector`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: keywords\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: ogImage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"VayuVector\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: fullTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: ogImage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: `https://vayuvector.com${ false ? 0 : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    noIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex, nofollow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"Organization\",\n                                \"name\": \"VayuVector\",\n                                \"description\": description,\n                                \"url\": \"https://vayuvector.com\",\n                                \"logo\": \"https://vayuvector.com/logo.svg\",\n                                \"contactPoint\": {\n                                    \"@type\": \"ContactPoint\",\n                                    \"telephone\": \"******-VAYU-VEC\",\n                                    \"contactType\": \"customer service\",\n                                    \"availableLanguage\": [\n                                        \"English\"\n                                    ]\n                                },\n                                \"sameAs\": [\n                                    \"https://facebook.com/vayuvector\",\n                                    \"https://twitter.com/vayuvector\",\n                                    \"https://linkedin.com/company/vayuvector\"\n                                ],\n                                \"address\": {\n                                    \"@type\": \"PostalAddress\",\n                                    \"streetAddress\": \"123 Logistics Ave\",\n                                    \"addressLocality\": \"Global City\",\n                                    \"addressRegion\": \"GC\",\n                                    \"postalCode\": \"12345\",\n                                    \"addressCountry\": \"US\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Layout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.tsx\n");

/***/ }),

/***/ "./components/QuoteCalculator.tsx":
/*!****************************************!*\
  !*** ./components/QuoteCalculator.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalculatorIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightIcon,CalculatorIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst QuoteCalculator = ()=>{\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        origin: {\n            country: \"\",\n            city: \"\"\n        },\n        destination: {\n            country: \"\",\n            city: \"\"\n        },\n        homeSize: \"\",\n        moveDate: \"\",\n        packingService: false,\n        storageNeeded: false,\n        storageDuration: 0\n    });\n    const handleInputChange = (field, value)=>{\n        if (field.includes(\".\")) {\n            const [parent, child] = field.split(\".\");\n            setFormData((prev)=>({\n                    ...prev,\n                    [parent]: {\n                        ...prev[parent] || {},\n                        [child]: value\n                    }\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [field]: value\n                }));\n        }\n    };\n    const calculateQuote = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/quotes/calculate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    origin: formData.origin,\n                    destination: formData.destination,\n                    homeSize: formData.homeSize,\n                    packingService: formData.packingService,\n                    storageNeeded: formData.storageNeeded,\n                    storageDuration: formData.storageDuration\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to calculate quote\");\n            }\n            const data = await response.json();\n            setResult(data.data);\n            setStep(4);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Quote calculated successfully!\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Failed to calculate quote. Please try again.\");\n            console.error(\"Quote calculation error:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const nextStep = ()=>{\n        if (step < 3) {\n            setStep(step + 1);\n        } else {\n            calculateQuote();\n        }\n    };\n    const prevStep = ()=>{\n        if (step > 1) {\n            setStep(step - 1);\n        }\n    };\n    const resetCalculator = ()=>{\n        setStep(1);\n        setResult(null);\n        setFormData({\n            origin: {\n                country: \"\",\n                city: \"\"\n            },\n            destination: {\n                country: \"\",\n                city: \"\"\n            },\n            homeSize: \"\",\n            moveDate: \"\",\n            packingService: false,\n            storageNeeded: false,\n            storageDuration: 0\n        });\n    };\n    const isStepValid = ()=>{\n        switch(step){\n            case 1:\n                return formData.origin.country && formData.origin.city && formData.destination.country && formData.destination.city;\n            case 2:\n                return formData.homeSize && formData.moveDate;\n            case 3:\n                return true; // Optional services\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card p-8 max-w-2xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-body\",\n                                children: [\n                                    \"Step \",\n                                    step,\n                                    \" of \",\n                                    result ? 4 : 3\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted\",\n                                children: [\n                                    step === 1 && \"Locations\",\n                                    step === 2 && \"Move Details\",\n                                    step === 3 && \"Additional Services\",\n                                    step === 4 && \"Your Quote\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-navy-600 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${step / (result ? 4 : 3) * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    x: 20\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                exit: {\n                    opacity: 0,\n                    x: -20\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    step === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl heading-secondary mb-4\",\n                                children: \"Where are you moving?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-body mb-2\",\n                                                children: \"Moving From\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Country\",\n                                                value: formData.origin.country,\n                                                onChange: (e)=>handleInputChange(\"origin.country\", e.target.value),\n                                                className: \"input mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"City\",\n                                                value: formData.origin.city,\n                                                onChange: (e)=>handleInputChange(\"origin.city\", e.target.value),\n                                                className: \"input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-body mb-2\",\n                                                children: \"Moving To\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Country\",\n                                                value: formData.destination.country,\n                                                onChange: (e)=>handleInputChange(\"destination.country\", e.target.value),\n                                                className: \"input mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"City\",\n                                                value: formData.destination.city,\n                                                onChange: (e)=>handleInputChange(\"destination.city\", e.target.value),\n                                                className: \"input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, undefined),\n                    step === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl heading-secondary mb-4\",\n                                children: \"Tell us about your move\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-body mb-2\",\n                                        children: \"Home Size\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: formData.homeSize,\n                                        onChange: (e)=>handleInputChange(\"homeSize\", e.target.value),\n                                        className: \"input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select home size\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"studio\",\n                                                children: \"Studio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"1-bedroom\",\n                                                children: \"1 Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"2-bedroom\",\n                                                children: \"2 Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"3-bedroom\",\n                                                children: \"3 Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"4-bedroom\",\n                                                children: \"4 Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"5-bedroom\",\n                                                children: \"5+ Bedroom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"office\",\n                                                children: \"Office\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-body mb-2\",\n                                        children: \"Preferred Move Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: formData.moveDate,\n                                        onChange: (e)=>handleInputChange(\"moveDate\", e.target.value),\n                                        className: \"input\",\n                                        min: new Date().toISOString().split(\"T\")[0]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined),\n                    step === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl heading-secondary mb-4\",\n                                children: \"Additional Services\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.packingService,\n                                                onChange: (e)=>handleInputChange(\"packingService\", e.target.checked),\n                                                className: \"rounded border-gray-300 text-navy-600 focus:ring-navy-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-body\",\n                                                children: \"Professional packing service (+30% of base cost)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.storageNeeded,\n                                                onChange: (e)=>handleInputChange(\"storageNeeded\", e.target.checked),\n                                                className: \"rounded border-gray-300 text-navy-600 focus:ring-navy-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-body\",\n                                                children: \"Storage service needed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    formData.storageNeeded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-body mb-2\",\n                                                children: \"Storage Duration (months)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                max: \"24\",\n                                                value: formData.storageDuration,\n                                                onChange: (e)=>handleInputChange(\"storageDuration\", parseInt(e.target.value) || 0),\n                                                className: \"input w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, undefined),\n                    step === 4 && result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalculatorIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalculatorIcon, {\n                                        className: \"h-12 w-12 text-navy-600 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl heading-main mb-2\",\n                                        children: \"Your Estimated Quote\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted\",\n                                        children: [\n                                            \"From \",\n                                            formData.origin.city,\n                                            \", \",\n                                            formData.origin.country,\n                                            \" to\",\n                                            \" \",\n                                            formData.destination.city,\n                                            \", \",\n                                            formData.destination.country\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-minimalist-alt rounded-lg p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Base moving cost:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-body\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.basePrice.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        result.packingPrice > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Packing service:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-body\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.packingPrice.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        result.storagePrice > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Storage service:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-body\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.storagePrice.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                            className: \"border-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-lg font-bold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-body\",\n                                                    children: \"Total Estimated Cost:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-navy-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        result.totalPrice.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"This is an estimate. Final pricing may vary based on actual inventory and services.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-primary\",\n                                                children: \"Get Detailed Quote\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: resetCalculator,\n                                                className: \"btn-outline\",\n                                                children: \"Calculate Again\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, step, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            step < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: prevStep,\n                        disabled: step === 1,\n                        className: \"btn-ghost disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: nextStep,\n                        disabled: !isStepValid() || loading,\n                        className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: step === 3 ? \"Calculate Quote\" : \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalculatorIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArrowRightIcon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\QuoteCalculator.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuoteCalculator);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/QuoteCalculator.tsx\n");

/***/ }),

/***/ "./components/ServicesCarousel.tsx":
/*!*****************************************!*\
  !*** ./components/ServicesCarousel.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArchiveBoxIcon,DocumentTextIcon,GlobeAltIcon,HomeIcon,ShieldCheckIcon,TruckIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_3__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nconst services = [\n    {\n        id: \"residential\",\n        title: \"Residential Relocation\",\n        description: \"Complete household moving services with professional packing and careful handling of your belongings.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HomeIcon,\n        features: [\n            \"Professional packing & unpacking\",\n            \"Furniture disassembly & assembly\",\n            \"Appliance disconnection & reconnection\",\n            \"Fragile item special handling\",\n            \"Room-by-room organization\"\n        ],\n        color: \"text-primary-600\",\n        bgColor: \"bg-primary-100\",\n        href: \"/services/residential\"\n    },\n    {\n        id: \"vehicle\",\n        title: \"Vehicle Transportation\",\n        description: \"Safe and secure transportation of your vehicles with full insurance coverage and tracking.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.TruckIcon,\n        features: [\n            \"Door-to-door car shipping\",\n            \"Motorcycle transportation\",\n            \"Classic & luxury vehicle handling\",\n            \"International documentation\",\n            \"Real-time tracking\"\n        ],\n        color: \"text-secondary-600\",\n        bgColor: \"bg-secondary-100\",\n        href: \"/services/vehicle\"\n    },\n    {\n        id: \"documentation\",\n        title: \"Documentation Support\",\n        description: \"Complete assistance with all paperwork, visas, and legal requirements for your move.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DocumentTextIcon,\n        features: [\n            \"Visa & work permit guidance\",\n            \"Customs clearance assistance\",\n            \"School enrollment support\",\n            \"Banking setup coordination\",\n            \"Local registration help\"\n        ],\n        color: \"text-accent-600\",\n        bgColor: \"bg-accent-100\",\n        href: \"/services/documentation\"\n    },\n    {\n        id: \"storage\",\n        title: \"Storage Solutions\",\n        description: \"Secure short-term and long-term storage options for your belongings during transition.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArchiveBoxIcon,\n        features: [\n            \"Climate-controlled facilities\",\n            \"Short & long-term options\",\n            \"Inventory management\",\n            \"24/7 security monitoring\",\n            \"Easy access scheduling\"\n        ],\n        color: \"text-purple-600\",\n        bgColor: \"bg-purple-100\",\n        href: \"/services/storage\"\n    },\n    {\n        id: \"global\",\n        title: \"Global Network\",\n        description: \"Worldwide coverage with local expertise in over 120 countries.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.GlobeAltIcon,\n        features: [\n            \"120+ countries served\",\n            \"Local partner network\",\n            \"Cultural expertise\",\n            \"Multi-language support\",\n            \"Time zone coordination\"\n        ],\n        color: \"text-green-600\",\n        bgColor: \"bg-green-100\",\n        href: \"/services\"\n    },\n    {\n        id: \"insurance\",\n        title: \"Insurance & Protection\",\n        description: \"Comprehensive insurance coverage and protection for your valuable belongings.\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_DocumentTextIcon_GlobeAltIcon_HomeIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShieldCheckIcon,\n        features: [\n            \"Full replacement value coverage\",\n            \"Transit insurance included\",\n            \"Damage claim processing\",\n            \"Third-party liability\",\n            \"Peace of mind guarantee\"\n        ],\n        color: \"text-indigo-600\",\n        bgColor: \"bg-indigo-100\",\n        href: \"/services\"\n    }\n];\nconst ServicesCarousel = ()=>{\n    const [activeService, setActiveService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Comprehensive Relocation Services\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: \"From residential moves to vehicle transportation, we provide end-to-end solutions for your international relocation needs.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: services.map((service, index)=>{\n                                const IconComponent = service.icon;\n                                const isActive = activeService === index;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    onClick: ()=>setActiveService(index),\n                                    className: `w-full text-left p-6 rounded-xl transition-all duration-300 ${isActive ? \"bg-white shadow-medium border-l-4 border-secondary-600\" : \"bg-gray-50 hover:bg-white hover:shadow-soft\"}`,\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex-shrink-0 w-12 h-12 rounded-lg ${service.bgColor} flex items-center justify-center`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: `h-6 w-6 ${service.color}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: `text-lg font-semibold mb-2 ${isActive ? \"text-secondary-600\" : \"text-gray-900\"}`,\n                                                        children: service.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: service.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, service.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:pl-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                className: \"card p-8 relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-32 h-32 opacity-5\",\n                                        children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(services[activeService].icon, {\n                                            className: \"h-full w-full\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-16 h-16 rounded-xl ${services[activeService].bgColor} flex items-center justify-center`,\n                                                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(services[activeService].icon, {\n                                                    className: `h-8 w-8 ${services[activeService].color}`\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: services[activeService].title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: services[activeService].description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-gray-900 mb-4\",\n                                                children: \"What's Included:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            services[activeService].features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3,\n                                                        delay: index * 0.1\n                                                    },\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-secondary-600 rounded-full flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-700\",\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 pt-6 border-t border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: services[activeService].href,\n                                            className: \"btn-secondary w-full sm:w-auto text-black font-semibold inline-block text-center\",\n                                            children: [\n                                                \"Learn More About \",\n                                                services[activeService].title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, activeService, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-primary-600 mb-2\",\n                                    children: \"50K+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Successful Moves\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-secondary-600 mb-2\",\n                                    children: \"120+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Countries Served\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-accent-600 mb-2\",\n                                    children: \"15+\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Years Experience\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl md:text-3xl font-bold text-green-600 mb-2\",\n                                    children: \"24/7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: \"Customer Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\ServicesCarousel.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ServicesCarousel);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ServicesCarousel.tsx\n");

/***/ }),

/***/ "./components/Testimonials.tsx":
/*!*************************************!*\
  !*** ./components/Testimonials.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon,StarIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst testimonials = [\n    {\n        id: 1,\n        name: \"Sarah Johnson\",\n        role: \"Software Engineer\",\n        company: \"Tech Corp\",\n        location: \"London, UK\",\n        rating: 5,\n        content: \"VayuVector made our international move from San Francisco to London absolutely seamless. Their team handled everything from packing our delicate electronics to navigating customs. The communication was excellent throughout the entire process.\",\n        avatar: \"/images/testimonials/sarah.jpg\",\n        moveRoute: \"San Francisco → London\"\n    },\n    {\n        id: 2,\n        name: \"Michael Chen\",\n        role: \"Marketing Director\",\n        company: \"Global Solutions\",\n        location: \"Singapore\",\n        rating: 5,\n        content: \"Outstanding service! They moved our entire household from New York to Singapore, including our car. Everything arrived in perfect condition and on time. The customer portal made tracking our shipment so easy.\",\n        avatar: \"/images/testimonials/michael.jpg\",\n        moveRoute: \"New York → Singapore\"\n    },\n    {\n        id: 3,\n        name: \"Emma Rodriguez\",\n        role: \"Research Scientist\",\n        company: \"BioTech Labs\",\n        location: \"Barcelona, Spain\",\n        rating: 5,\n        content: \"The documentation support was incredible. VayuVector helped us with all the visa paperwork and customs requirements. They truly understand the challenges of international relocation.\",\n        avatar: \"/images/testimonials/emma.jpg\",\n        moveRoute: \"Boston → Barcelona\"\n    },\n    {\n        id: 4,\n        name: \"David Thompson\",\n        role: \"Finance Manager\",\n        company: \"Investment Bank\",\n        location: \"Tokyo, Japan\",\n        rating: 5,\n        content: \"Professional, reliable, and stress-free. The team went above and beyond to ensure our move to Tokyo was smooth. They even helped us find temporary accommodation while we searched for a permanent home.\",\n        avatar: \"/images/testimonials/david.jpg\",\n        moveRoute: \"Chicago → Tokyo\"\n    },\n    {\n        id: 5,\n        name: \"Lisa Anderson\",\n        role: \"Product Manager\",\n        company: \"StartupCo\",\n        location: \"Sydney, Australia\",\n        rating: 5,\n        content: \"VayuVector exceeded all our expectations. The packing was meticulous, the shipping was fast, and the unpacking service saved us weeks of work. Highly recommend for any international move!\",\n        avatar: \"/images/testimonials/lisa.jpg\",\n        moveRoute: \"Seattle → Sydney\"\n    }\n];\nconst Testimonials = ()=>{\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Auto-advance testimonials\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAutoPlaying) return;\n        const interval = setInterval(()=>{\n            setCurrentIndex((prevIndex)=>prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1);\n        }, 5000);\n        return ()=>clearInterval(interval);\n    }, [\n        isAutoPlaying\n    ]);\n    const goToNext = ()=>{\n        setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);\n    };\n    const goToPrevious = ()=>{\n        setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);\n    };\n    const goToSlide = (index)=>{\n        setCurrentIndex(index);\n    };\n    const currentTestimonial = testimonials[currentIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"What Our Customers Say\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Don't just take our word for it. Here's what professionals around the world say about their VayuVector experience.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            onMouseEnter: ()=>setIsAutoPlaying(false),\n                            onMouseLeave: ()=>setIsAutoPlaying(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: -20\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        className: \"card p-8 md:p-12 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center mb-6\",\n                                                children: [\n                                                    ...Array(currentTestimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.StarIcon, {\n                                                        className: \"h-6 w-6 text-accent-500\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-lg md:text-xl text-gray-700 mb-8 leading-relaxed\",\n                                                children: [\n                                                    '\"',\n                                                    currentTestimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gradient-to-br from-primary-400 to-secondary-600 rounded-full flex items-center justify-center text-white font-bold text-xl\",\n                                                            children: currentTestimonial.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center md:text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-semibold text-gray-900 text-lg\",\n                                                                children: currentTestimonial.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-600\",\n                                                                children: [\n                                                                    currentTestimonial.role,\n                                                                    \" at \",\n                                                                    currentTestimonial.company\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: currentTestimonial.moveRoute\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, currentIndex, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToPrevious,\n                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-white shadow-medium hover:shadow-hard transition-all duration-200 text-gray-600 hover:text-primary-600\",\n                                    \"aria-label\": \"Previous testimonial\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChevronLeftIcon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToNext,\n                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-white shadow-medium hover:shadow-hard transition-all duration-200 text-gray-600 hover:text-primary-600\",\n                                    \"aria-label\": \"Next testimonial\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.ChevronRightIcon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-8 space-x-2\",\n                            children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>goToSlide(index),\n                                    className: `w-3 h-3 rounded-full transition-all duration-200 ${index === currentIndex ? \"bg-primary-600 scale-110\" : \"bg-gray-300 hover:bg-gray-400\"}`,\n                                    \"aria-label\": `Go to testimonial ${index + 1}`\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-primary-600 mb-2\",\n                                            children: \"4.9/5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Average Rating\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mt-2\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__.StarIcon, {\n                                                    className: \"h-4 w-4 text-accent-500\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-primary-600 mb-2\",\n                                            children: \"2,500+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Happy Customers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-primary-600 mb-2\",\n                                            children: \"98%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Would Recommend\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\Testimonials.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Testimonials);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Testimonials.tsx\n");

/***/ }),

/***/ "./components/WorldMap.tsx":
/*!*********************************!*\
  !*** ./components/WorldMap.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst mapPins = [\n    // North America\n    {\n        id: \"nyc\",\n        name: \"New York\",\n        country: \"USA\",\n        x: 25,\n        y: 35,\n        moves: 1250,\n        popular: true\n    },\n    {\n        id: \"sf\",\n        name: \"San Francisco\",\n        country: \"USA\",\n        x: 15,\n        y: 38,\n        moves: 980,\n        popular: true\n    },\n    {\n        id: \"toronto\",\n        name: \"Toronto\",\n        country: \"Canada\",\n        x: 23,\n        y: 32,\n        moves: 650,\n        popular: false\n    },\n    // Europe\n    {\n        id: \"london\",\n        name: \"London\",\n        country: \"UK\",\n        x: 50,\n        y: 30,\n        moves: 1450,\n        popular: true\n    },\n    {\n        id: \"paris\",\n        name: \"Paris\",\n        country: \"France\",\n        x: 52,\n        y: 32,\n        moves: 890,\n        popular: false\n    },\n    {\n        id: \"berlin\",\n        name: \"Berlin\",\n        country: \"Germany\",\n        x: 55,\n        y: 30,\n        moves: 720,\n        popular: false\n    },\n    {\n        id: \"zurich\",\n        name: \"Zurich\",\n        country: \"Switzerland\",\n        x: 54,\n        y: 33,\n        moves: 560,\n        popular: false\n    },\n    // Asia\n    {\n        id: \"tokyo\",\n        name: \"Tokyo\",\n        country: \"Japan\",\n        x: 85,\n        y: 38,\n        moves: 1100,\n        popular: true\n    },\n    {\n        id: \"singapore\",\n        name: \"Singapore\",\n        country: \"Singapore\",\n        x: 78,\n        y: 55,\n        moves: 950,\n        popular: true\n    },\n    {\n        id: \"hongkong\",\n        name: \"Hong Kong\",\n        country: \"China\",\n        x: 82,\n        y: 45,\n        moves: 840,\n        popular: false\n    },\n    {\n        id: \"dubai\",\n        name: \"Dubai\",\n        country: \"UAE\",\n        x: 65,\n        y: 45,\n        moves: 670,\n        popular: false\n    },\n    // Australia\n    {\n        id: \"sydney\",\n        name: \"Sydney\",\n        country: \"Australia\",\n        x: 88,\n        y: 75,\n        moves: 780,\n        popular: true\n    },\n    {\n        id: \"melbourne\",\n        name: \"Melbourne\",\n        country: \"Australia\",\n        x: 86,\n        y: 78,\n        moves: 520,\n        popular: false\n    }\n];\nconst WorldMap = ()=>{\n    const [hoveredPin, setHoveredPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPin, setSelectedPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handlePinClick = (pinId)=>{\n        setSelectedPin(selectedPin === pinId ? null : pinId);\n    };\n    const getSelectedPinData = ()=>{\n        return mapPins.find((pin)=>pin.id === selectedPin);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section bg-gradient-to-br from-primary-50 to-secondary-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Global Coverage, Local Expertise\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: \"With our worldwide network of partners and agents, we provide seamless relocation services to over 120 countries. Click on the pins to explore our most popular destinations.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-96 md:h-[500px] bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl overflow-hidden shadow-soft\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                viewBox: \"0 0 1000 500\",\n                                                className: \"w-full h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M150 200 L300 180 L350 220 L320 280 L200 300 L150 250 Z\",\n                                                        fill: \"#1E3A8A\",\n                                                        opacity: \"0.3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M400 150 L600 140 L650 200 L620 250 L500 260 L450 200 Z\",\n                                                        fill: \"#1E3A8A\",\n                                                        opacity: \"0.3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M700 200 L850 190 L900 240 L880 300 L750 310 L700 250 Z\",\n                                                        fill: \"#1E3A8A\",\n                                                        opacity: \"0.3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M800 350 L900 340 L920 380 L900 420 L820 430 L800 390 Z\",\n                                                        fill: \"#1E3A8A\",\n                                                        opacity: \"0.3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        mapPins.map((pin)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                className: \"absolute cursor-pointer\",\n                                                style: {\n                                                    left: `${pin.x}%`,\n                                                    top: `${pin.y}%`,\n                                                    transform: \"translate(-50%, -50%)\"\n                                                },\n                                                onMouseEnter: ()=>setHoveredPin(pin.id),\n                                                onMouseLeave: ()=>setHoveredPin(null),\n                                                onClick: ()=>handlePinClick(pin.id),\n                                                whileHover: {\n                                                    scale: 1.2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-4 h-4 rounded-full border-2 border-white shadow-medium transition-all duration-200 ${pin.popular ? \"bg-accent-500\" : \"bg-primary-600\"} ${hoveredPin === pin.id || selectedPin === pin.id ? \"scale-150 shadow-glow\" : \"\"}`,\n                                                        children: pin.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 rounded-full bg-accent-500 animate-ping opacity-75\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    hoveredPin === pin.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 10\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap shadow-hard\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: pin.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-300\",\n                                                                children: [\n                                                                    pin.moves,\n                                                                    \" moves completed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, pin.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"absolute inset-0 w-full h-full pointer-events-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"connectionGradient\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"0%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#0891B2\",\n                                                                stopOpacity: \"0.3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#F97316\",\n                                                                stopOpacity: \"0.6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#1E3A8A\",\n                                                                stopOpacity: \"0.3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: `M ${mapPins[0].x * 10} ${mapPins[0].y * 5} Q 400 150 ${mapPins[3].x * 10} ${mapPins[3].y * 5}`,\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\",\n                                                    className: \"animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: `M ${mapPins[3].x * 10} ${mapPins[3].y * 5} Q 650 300 ${mapPins[7].x * 10} ${mapPins[7].y * 5}`,\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\",\n                                                    className: \"animate-pulse\",\n                                                    style: {\n                                                        animationDelay: \"1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedPin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mt-8 card p-6\",\n                                    children: (()=>{\n                                        const pinData = getSelectedPinData();\n                                        if (!pinData) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900\",\n                                                            children: [\n                                                                pinData.name,\n                                                                \", \",\n                                                                pinData.country\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mt-1\",\n                                                            children: [\n                                                                pinData.moves,\n                                                                \" successful relocations completed\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        pinData.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-block mt-2 px-3 py-1 bg-accent-100 text-accent-700 text-sm font-medium rounded-full\",\n                                                            children: \"Popular Destination\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-primary\",\n                                                    children: [\n                                                        \"Get Quote to \",\n                                                        pinData.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-primary-600 mb-2\",\n                                            children: \"120+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Countries Served\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-secondary-600 mb-2\",\n                                            children: \"500+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Partner Agents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-accent-600 mb-2\",\n                                            children: \"50K+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Moves Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-green-600 mb-2\",\n                                            children: \"99.2%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Success Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\components\\\\WorldMap.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WorldMap);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/WorldMap.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query/devtools */ \"react-query/devtools\");\n/* harmony import */ var react_query_devtools__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    retry: 1,\n                    refetchOnWindowFocus: false,\n                    staleTime: 5 * 60 * 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#10B981\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#EF4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query_devtools__WEBPACK_IMPORTED_MODULE_2__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                        as: \"style\",\n                        onLoad: (e)=>{\n                            const target = e.target;\n                            target.onload = null;\n                            target.rel = \"stylesheet\";\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                            rel: \"stylesheet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://api.vayuvector.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://www.google-analytics.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://maps.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1E3A8A\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#1E3A8A\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.tsx\");\n/* harmony import */ var _components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/QuoteCalculator */ \"./components/QuoteCalculator.tsx\");\n/* harmony import */ var _components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ServicesCarousel */ \"./components/ServicesCarousel.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Testimonials */ \"./components/Testimonials.tsx\");\n/* harmony import */ var _components_WorldMap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/WorldMap */ \"./components/WorldMap.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__, _components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__, _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__, _components_WorldMap__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__, _components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__, _components_Testimonials__WEBPACK_IMPORTED_MODULE_6__, _components_WorldMap__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst HomePage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"VayuVector - Your Global Relocation Partner\",\n        description: \"Professional international relocation services. Moving lives, not just belongings. Door-to-door service with comprehensive support for expats and professionals worldwide.\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative min-h-screen flex items-center justify-center bg-white overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-hero-pattern\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-8 gap-4 w-full h-full p-8 opacity-10\",\n                                    children: [\n                                        ...Array(64)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-200 rounded-full opacity-30 animate-pulse\",\n                                            style: {\n                                                animationDelay: `${i * 0.1}s`\n                                            }\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-64 h-64 bg-gray-100/30 rounded-full blur-3xl animate-float\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-gray-200/30 rounded-full blur-3xl animate-float\",\n                                style: {\n                                    animationDelay: \"1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 container-custom text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl heading-main mb-6 animate-fade-in-up\",\n                                    children: [\n                                        \"Your Global\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-navy-600\",\n                                            children: \"Relocation Partner\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl lg:text-3xl mb-8 heading-secondary animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.2s\"\n                                    },\n                                    children: \"Moving Lives, Not Just Belongings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg md:text-xl mb-12 text-body max-w-3xl mx-auto animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.4s\"\n                                    },\n                                    children: \"Professional door-to-door relocation services for international professionals. From packing to destination setup, we handle every detail of your global move.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.6s\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/quote\",\n                                            className: \"bg-navy-700 text-white font-semibold text-lg px-8 py-4 rounded-lg hover:bg-navy-800 transition-colors duration-200\",\n                                            children: \"Get Free Quote\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/how-it-works\",\n                                            className: \"border-2 border-navy-600 text-navy-600 font-semibold text-lg px-8 py-4 rounded-lg hover:bg-navy-600 hover:text-white transition-colors duration-200\",\n                                            children: \"How It Works\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 animate-fade-in-up\",\n                                    style: {\n                                        animationDelay: \"0.8s\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-navy-600 mb-2\",\n                                                    children: \"15+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Years Experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-navy-600 mb-2\",\n                                                    children: \"50K+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Successful Moves\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-navy-600 mb-2\",\n                                                    children: \"120+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Countries Served\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"section bg-minimalist-alt\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl heading-main mb-4\",\n                                    children: \"Get Your Moving Quote in Minutes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-muted\",\n                                    children: \"Tell us about your move and get an instant estimate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuoteCalculator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServicesCarousel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WorldMap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\augument\\\\base\\\\vayuvector\\\\frontend\\\\pages\\\\index.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-query":
/*!******************************!*\
  !*** external "react-query" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ "react-query/devtools":
/*!***************************************!*\
  !*** external "react-query/devtools" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query/devtools");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();