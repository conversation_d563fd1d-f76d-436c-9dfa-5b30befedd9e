import React from 'react';
import Link from 'next/link';
import Layout from '@/components/Layout';
import { 
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  GlobeAltIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  AcademicCapIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';

const DocumentationSupportPage: React.FC = () => {
  const documentationServices = [
    {
      title: 'Visa & Immigration Support',
      description: 'Complete guidance through visa applications and immigration processes.',
      features: [
        'Visa application assistance',
        'Work permit processing',
        'Family visa coordination',
        'Immigration law guidance',
        'Document preparation',
        'Application tracking',
        'Interview preparation',
        'Status updates'
      ],
      icon: DocumentTextIcon,
    },
    {
      title: 'Customs & Legal Documentation',
      description: 'Expert handling of all customs and legal paperwork for international moves.',
      features: [
        'Customs declaration forms',
        'Import/export permits',
        'Legal document translation',
        'Notarization services',
        'Apostille certification',
        'Power of attorney setup',
        'Insurance documentation',
        'Compliance verification'
      ],
      icon: ShieldCheckIcon,
    },
    {
      title: 'Educational Support',
      description: 'Comprehensive assistance with school and university enrollment processes.',
      features: [
        'School research and selection',
        'Enrollment application support',
        'Academic record transfers',
        'Language proficiency tests',
        'Scholarship applications',
        'Student visa assistance',
        'Housing arrangements',
        'Orientation support'
      ],
      icon: AcademicCapIcon,
    },
    {
      title: 'Financial & Banking Setup',
      description: 'Complete support for establishing financial services in your new country.',
      features: [
        'Bank account opening',
        'Credit history transfer',
        'Tax registration',
        'Insurance transfers',
        'Investment account setup',
        'Currency exchange guidance',
        'Financial planning advice',
        'Local banking orientation'
      ],
      icon: BanknotesIcon,
    },
  ];

  const processSteps = [
    {
      step: '1',
      title: 'Initial Consultation',
      description: 'We assess your specific documentation needs based on your destination and circumstances.',
      icon: UserGroupIcon,
    },
    {
      step: '2',
      title: 'Document Preparation',
      description: 'Our experts prepare and organize all required documents with proper formatting and translations.',
      icon: DocumentTextIcon,
    },
    {
      step: '3',
      title: 'Application Submission',
      description: 'We handle the submission process and track progress through all relevant authorities.',
      icon: CheckCircleIcon,
    },
    {
      step: '4',
      title: 'Follow-up & Support',
      description: 'Continuous support until all documentation is approved and processes are complete.',
      icon: ClockIcon,
    },
  ];

  const benefits = [
    'Expert knowledge of international regulations',
    'Reduced processing times',
    'Minimized risk of application rejection',
    'Stress-free documentation process',
    'Multi-language support',
    'Local expertise in destination countries',
    'Comprehensive compliance assurance',
    '24/7 support throughout the process'
  ];

  return (
    <Layout
      title="Documentation Support Services - VayuVector"
      description="Complete documentation and administrative support for international relocations. Visa assistance, customs paperwork, school enrollment, and banking setup with expert guidance."
    >
      {/* Hero Section */}
      <section className="pt-16 pb-16 bg-gradient-to-br from-gray-50 to-accent-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <DocumentTextIcon className="h-12 w-12 text-accent-600" />
                <span className="text-accent-600 font-semibold text-lg">Documentation Support</span>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Complete Documentation & Administrative Support
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8">
                Navigate complex international paperwork with confidence. From visa applications 
                to school enrollment and banking setup, our experts handle all documentation 
                requirements for your smooth relocation.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/quote" className="btn-accent text-white font-semibold text-lg px-8 py-3">
                  Get Documentation Quote
                </Link>
                <a href="tel:******-VAYU-VEC" className="btn-outline border-accent-600 text-accent-600 hover:bg-accent-600 hover:text-white text-lg px-8 py-3">
                  Speak to Expert
                </a>
              </div>
            </div>
            <div className="aspect-video bg-gradient-to-br from-accent-100 to-accent-200 rounded-2xl flex items-center justify-center relative overflow-hidden">
              {/* Document stack background */}
              <div className="absolute inset-0 opacity-10">
                <div className="flex flex-col items-center justify-center h-full space-y-2">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className={`w-32 h-8 bg-accent-600 rounded transform rotate-${i * 2} opacity-30`}></div>
                  ))}
                </div>
              </div>
              <div className="relative z-10 text-center">
                <DocumentTextIcon className="h-24 w-24 text-accent-600 opacity-70 mx-auto mb-4" />
                <div className="text-accent-700 font-semibold">Complete Documentation Support</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Documentation Services
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our documentation support covers every aspect of international relocation paperwork, 
              ensuring compliance and smooth processing.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {documentationServices.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <div key={index} className="card p-8 hover:shadow-medium transition-shadow duration-300">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-6 w-6 text-accent-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">{service.title}</h3>
                  </div>
                  <p className="text-gray-600 mb-6">{service.description}</p>
                  <div className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-accent-600 rounded-full flex-shrink-0"></div>
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Steps */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Documentation Process
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              A systematic approach to handling all your documentation needs with expert guidance at every step.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => {
              const IconComponent = step.icon;
              return (
                <div key={index} className="text-center">
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-accent-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-secondary-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{step.step}</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{step.title}</h3>
                  <p className="text-gray-600 text-sm">{step.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="section">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Choose Our Documentation Support?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                International documentation can be complex and time-consuming. Our expert team 
                simplifies the process and ensures everything is handled correctly the first time.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircleIcon className="h-5 w-5 text-accent-600 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="aspect-square bg-gradient-to-br from-accent-100 to-accent-200 rounded-2xl flex items-center justify-center">
              <GlobeAltIcon className="h-32 w-32 text-accent-600 opacity-50" />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-900 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Simplify Your Documentation Process?
          </h2>
          <p className="text-lg mb-8 text-gray-300 max-w-2xl mx-auto">
            Let our experts handle all your international documentation needs. 
            Get started with a consultation to discuss your specific requirements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/quote" className="btn bg-accent-600 text-white hover:bg-accent-700 text-lg px-8 py-3 font-semibold">
              Get Free Consultation
            </Link>
            <a href="tel:******-VAYU-VEC" className="btn border-2 border-white text-white hover:bg-white hover:text-primary-900 text-lg px-8 py-3">
              Call ******-VAYU-VEC
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default DocumentationSupportPage;
