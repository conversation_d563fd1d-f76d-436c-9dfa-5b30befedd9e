import React from 'react';
import Layout from '@/components/Layout';
import Link from 'next/link';
import { 
  HomeIcon,
  CheckCircleIcon,
  ClockIcon,
  ShieldCheckIcon,
  TruckIcon,
  ArchiveBoxIcon,
  ArrowRightIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

const ResidentialRelocationPage: React.FC = () => {
  const services = [
    {
      title: 'Professional Packing',
      description: 'Expert packing using high-quality materials to ensure your belongings arrive safely. Our trained packers use industry-leading techniques and materials.',
      features: [
        'Room-by-room systematic packing approach',
        'Specialized materials for fragile items (bubble wrap, foam, custom boxes)',
        'Custom wooden crating for artwork, antiques, and valuable items',
        'Detailed inventory documentation with photos',
        'Color-coded labeling system for easy unpacking',
        'Wardrobe boxes for clothing to prevent wrinkles',
        'Dish packs and cell kits for kitchenware',
        'Electronics packing with anti-static materials'
      ],
      icon: ArchiveBoxIcon,
    },
    {
      title: 'Furniture Handling',
      description: 'Professional disassembly, transport, and reassembly of all furniture items with expert care and precision.',
      features: [
        'Careful disassembly with labeled hardware storage',
        'Protective wrapping and padding for all surfaces',
        'Special handling protocols for antiques and heirlooms',
        'Detailed assembly instructions documentation with photos',
        'Quality assurance checks before and after transport',
        'Furniture placement according to your floor plan',
        'Touch-up and repair services if needed',
        'Specialized equipment for heavy items (pianos, safes)'
      ],
      icon: HomeIcon,
    },
    {
      title: 'Appliance Services',
      description: 'Safe disconnection, transport, and reconnection of all household appliances.',
      features: [
        'Professional disconnection service',
        'Secure transportation methods',
        'Installation at destination',
        'Electrical safety compliance',
        'Warranty protection maintained'
      ],
      icon: TruckIcon,
    },
    {
      title: 'Storage Solutions',
      description: 'Flexible storage options for items that need temporary housing.',
      features: [
        'Climate-controlled facilities',
        'Short and long-term options',
        '24/7 security monitoring',
        'Easy access scheduling',
        'Inventory management system'
      ],
      icon: ArchiveBoxIcon,
    },
  ];

  const process = [
    {
      step: '01',
      title: 'Initial Survey',
      description: 'Comprehensive assessment of your belongings and moving requirements.',
      details: 'Our expert surveyors visit your home to catalog all items, assess special requirements, and provide accurate volume calculations for precise quotations.'
    },
    {
      step: '02',
      title: 'Custom Planning',
      description: 'Detailed moving plan tailored to your specific needs and timeline.',
      details: 'We create a comprehensive moving plan including packing schedule, transportation logistics, and destination delivery timeline.'
    },
    {
      step: '03',
      title: 'Professional Packing',
      description: 'Expert packing using premium materials and proven techniques.',
      details: 'Our trained packers use industry-best materials and techniques to ensure maximum protection for all your belongings during transit.'
    },
    {
      step: '04',
      title: 'Secure Transport',
      description: 'Safe and tracked transportation to your new destination.',
      details: 'Your belongings are transported in secure vehicles with real-time tracking and comprehensive insurance coverage.'
    },
    {
      step: '05',
      title: 'Destination Setup',
      description: 'Unpacking and setup services at your new home.',
      details: 'We unpack, place items according to your preferences, and ensure everything is set up perfectly in your new home.'
    },
  ];

  return (
    <Layout
      title="Residential Relocation Services - VayuVector"
      description="Comprehensive residential moving services for international relocations. Professional packing, furniture handling, and destination setup with full insurance coverage."
    >
      {/* Hero Section */}
      <section className="pt-16 pb-16 bg-gradient-to-br from-gray-50 to-secondary-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <HomeIcon className="h-12 w-12 text-secondary-600" />
                <span className="text-secondary-600 font-semibold text-lg">Residential Relocation</span>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Complete Household Moving Services
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8">
                From studio apartments to large family homes, we provide comprehensive
                residential relocation services that ensure your belongings arrive safely
                and your new home is set up perfectly. Our experienced team handles everything
                from delicate china to heavy furniture, ensuring a stress-free transition to your new home.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/quote" className="btn-secondary text-black font-semibold text-lg px-8 py-3">
                  Get Free Quote
                </Link>
                <a href="tel:******-VAYU-VEC" className="btn-outline border-secondary-600 text-secondary-600 hover:bg-secondary-600 hover:text-black text-lg px-8 py-3">
                  Call Now
                </a>
              </div>
            </div>
            <div className="aspect-video bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl flex items-center justify-center relative overflow-hidden">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="grid grid-cols-4 gap-4 p-4 h-full">
                  {[...Array(12)].map((_, i) => (
                    <div key={i} className="bg-secondary-600 rounded opacity-30"></div>
                  ))}
                </div>
              </div>
              <div className="relative z-10 text-center">
                <HomeIcon className="h-24 w-24 text-secondary-600 opacity-70 mx-auto mb-4" />
                <div className="text-secondary-700 font-semibold">Professional Home Moving</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What's Included in Our Service
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our residential relocation service covers every aspect of your move, 
              ensuring a stress-free transition to your new home.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <div key={index} className="card p-8">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-6 w-6 text-secondary-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      {service.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-6">
                    {service.description}
                  </p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircleIcon className="h-5 w-5 text-secondary-600 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Proven Process
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We follow a systematic approach to ensure your residential move 
              is executed flawlessly from start to finish.
            </p>
          </div>

          <div className="space-y-8">
            {process.map((item, index) => (
              <div key={index} className="card p-8">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 items-center">
                  <div className="text-center lg:text-left">
                    <div className="w-16 h-16 bg-secondary-600 text-black rounded-full flex items-center justify-center mx-auto lg:mx-0 mb-4 text-xl font-bold">
                      {item.step}
                    </div>
                  </div>
                  <div className="lg:col-span-3">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {item.title}
                    </h3>
                    <p className="text-gray-600 mb-3">
                      {item.description}
                    </p>
                    <p className="text-sm text-gray-500">
                      {item.details}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Choose Our Residential Service?
              </h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <ShieldCheckIcon className="h-8 w-8 text-secondary-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Full Insurance Coverage
                    </h3>
                    <p className="text-gray-600">
                      Comprehensive insurance protection for all your belongings during the entire moving process.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <ClockIcon className="h-8 w-8 text-secondary-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Flexible Scheduling
                    </h3>
                    <p className="text-gray-600">
                      We work around your schedule to ensure minimal disruption to your daily life.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <CheckCircleIcon className="h-8 w-8 text-secondary-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Quality Guarantee
                    </h3>
                    <p className="text-gray-600">
                      We guarantee the quality of our service and will address any concerns promptly.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="aspect-square bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl flex items-center justify-center">
              <TruckIcon className="h-32 w-32 text-secondary-600 opacity-50" />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-900 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Start Your Residential Move?
          </h2>
          <p className="text-lg mb-8 text-gray-300 max-w-2xl mx-auto">
            Get a personalized quote for your residential relocation. Our experts 
            will assess your needs and provide a comprehensive moving plan.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/quote" className="btn bg-secondary-600 text-black hover:bg-secondary-700 text-lg px-8 py-3 font-semibold">
              Get Free Quote
            </Link>
            <a href="tel:******-VAYU-VEC" className="btn border-2 border-secondary-600 text-secondary-600 hover:bg-secondary-600 hover:text-black text-lg px-8 py-3">
              <PhoneIcon className="h-5 w-5 inline mr-2" />
              Call ******-VAYU-VEC
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default ResidentialRelocationPage;
